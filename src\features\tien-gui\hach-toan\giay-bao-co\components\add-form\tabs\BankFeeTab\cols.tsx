import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import {
  khachHangSearchColumns,
  accountSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  boPhanSearchColumns,
  chiPhiSearchColumns,
  QUERY_KEYS
} from '@/constants';
import { KhachHang, TaiKhoan, HopDong, DotThanhToan, KheUoc, Phi, VatTu } from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getBankFeeColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: (params: any) => (
      <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} disabled />
    )
  },
  {
    field: 'ma_cpnh',
    headerName: 'Mã chi phí',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí'
        value={params.row.ma_cpnh_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_cpnh_data', row)}
      />
    )
  },
  {
    field: 'ten_cpnh',
    headerName: 'Tên chi phí ngân hàng',
    width: 200,
    renderCell: (params: any) => (
      <CellField name='ten_cpnh' type='text' value={params.row.ma_cpnh_data?.ten_cp || ''} disabled />
    )
  },
  {
    field: 'tien_cp_nt',
    headerName: 'Phí VNĐ',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='tien_cp_nt'
        type='number'
        value={params.row.tien_cp_nt || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'tien_cp_nt', newValue)}
      />
    )
  },
  {
    field: 'so_ct0',
    headerName: 'Số hoá đơn',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'so_ct0', newValue)}
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='so_ct2'
        type='text'
        value={params.row.so_ct2 || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'so_ct2', newValue)}
      />
    )
  },
  {
    field: 'invoiceDate',
    headerName: 'Ngày hóa đơn',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='invoiceDate'
        type='date'
        value={params.row.invoiceDate || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'invoiceDate', newValue)}
      />
    )
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200,
    flex: 1,
    renderCell: (params: any) => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'tk_cpnh',
    headerName: 'Tk phí',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_cpnh_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'tk_cpnh_data', row)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'Tk đối ứng',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_du_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'tk_du_data', row)}
      />
    )
  },
  {
    field: 'tk_thue',
    headerName: 'Tk thuế',
    width: 150,
    renderCell: (params: any) => <CellField name='tk_thue' type='text' value={params.row.tk_thue || ''} disabled />
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 150,
    renderCell: (params: any) => <CellField name='ma_thue' type='number' value={params.row.ma_thue || 0} disabled />
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VNĐ',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 't_thue_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={[
          { field: 'ma_lsx', headerName: 'Mã lệnh SX', width: 150 },
          { field: 'ten_lsx', headerName: 'Tên lệnh SX', width: 250 }
        ]}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_cp0_data', row)}
      />
    )
  }
]; // Fixed infinite re-render issue
