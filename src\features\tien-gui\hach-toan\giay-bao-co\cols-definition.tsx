import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getChangingValueCreditAdviceColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 100 },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 100 },
  { field: 'ma_kh', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_kh', headerName: 'Tên đối tượng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'tknh', headerName: 'Tk ngân hàng', width: 120 },
  { field: 'tk', headerName: 'Tài khoản', width: 100 },
  { field: 't_tien_nt', headerName: 'Tổng tiền', width: 120 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 120 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 100 }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 300,
    renderCell: params => params.row.dien_giai
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 120,
    renderCell: params => params.row.tk_no_data?.code
  },
  {
    field: 'ty_gia2',
    headerName: 'Tỷ giá gs',
    width: 100,
    type: 'number'
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    type: 'number',
    renderCell: params => params.row.tien_nt?.toLocaleString('vi-VN')
  },
  {
    field: 'ma_bp',
    headerName: 'Mã bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },
  {
    field: 'ma_vv',
    headerName: 'Mã vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vv
  },
  {
    field: 'ma_hd',
    headerName: 'Mã hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },
  {
    field: 'ma_dtt',
    headerName: 'Mã đợt thanh toán',
    width: 150,
    renderCell: params => params.row.ma_dtt_data?.ma_dtt
  },
  {
    field: 'ma_ku',
    headerName: 'Mã khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku
  },
  {
    field: 'ma_phi',
    headerName: 'Mã phí',
    width: 120,
    renderCell: params => params.row.ma_phi_data?.ma_phi
  },
  {
    field: 'ma_sp',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },
  {
    field: 'ma_cp0',
    headerName: 'Mã chi phí không hợp lệ',
    width: 180,
    renderCell: params => params.row.ma_cp0_data?.ma_cp0
  }
];

export const exportCreditAdviceDetailColumns: GridColDef[] = [
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  { field: 'ma_dt', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_dt', headerName: 'Tên đối tượng', width: 200 },
  { field: 'du_no', headerName: 'Dư công nợ', type: 'number', width: 120 },
  { field: 'tk_co', headerName: 'Tài khoản có', width: 120 },
  { field: 'ten_tk', headerName: 'Tên tài khoản', width: 200 }
];
