import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getChangingValueCreditAdviceColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'status', headerName: 'Trạng thái', width: 100 },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 100 },
  { field: 'ma_kh', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_kh', headerName: 'Tên đối tượng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'tknh', headerName: 'Tk ngân hàng', width: 120 },
  { field: 'tk', headerName: 'Tài khoản', width: 100 },
  { field: 't_tien_nt', headerName: 'Tổng tiền', width: 120 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 120 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 100 }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 300,
    renderCell: params => params.row.dien_giai
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 120,
    renderCell: params => params.row.tk_no_data?.code
  },
  {
    field: 'ty_gia2',
    headerName: 'Tỷ giá gs',
    width: 100,
    type: 'number'
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    type: 'number',
    renderCell: params => params.row.tien_nt?.toLocaleString('vi-VN')
  },
  {
    field: 'ma_loai_hd',
    headerName: 'Loại hóa đơn',
    width: 120,
    renderCell: params => {
      const value = params.row.ma_loai_hd;
      switch (value) {
        case '0':
          return '0. Không có hoá đơn';
        case '1':
          return '1. Hoá đơn GTGT đã tách thuế';
        case '2':
          return '2. Hoá đơn GTGT không tách thuế';
        case '3':
          return '3. Hoá đơn bán hàng thông thường';
        default:
          return '';
      }
    }
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mã mẫu báo cáo',
    width: 150,
    renderCell: params => {
      const value = params.row.ma_mau_bc;
      switch (value) {
        case '3':
          return '3. Hóa đơn giá trị gia tăng';
        case '4':
          return '4. Hàng hóa, dịch vụ mua vào không có hóa đơn';
        case '5':
          return '5. Hóa đơn bán hàng thông thường';
        default:
          return '';
      }
    }
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất thuế',
    width: 120,
    renderCell: params => {
      const value = params.row.ma_tc_thue;
      switch (value) {
        case '1':
          return '1. Hàng hoá, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế';
        case '2':
          return '2. Hàng hoá, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT';
        case '3':
          return '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế';
        default:
          return '';
      }
    }
  },
  {
    field: 'ma_bp',
    headerName: 'Mã bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },
  {
    field: 'ma_vv',
    headerName: 'Mã vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vv
  },
  {
    field: 'ma_hd',
    headerName: 'Mã hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },
  {
    field: 'ma_dtt',
    headerName: 'Mã đợt thanh toán',
    width: 150,
    renderCell: params => params.row.ma_dtt_data?.ma_dtt
  },
  {
    field: 'ma_ku',
    headerName: 'Mã khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku
  },
  {
    field: 'ma_phi',
    headerName: 'Mã phí',
    width: 120,
    renderCell: params => params.row.ma_phi_data?.ma_phi
  },
  {
    field: 'ma_sp',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },
  {
    field: 'ma_cp0',
    headerName: 'Mã chi phí không hợp lệ',
    width: 180,
    renderCell: params => params.row.ma_cp0_data?.ma_cp0
  }
];

export const exportCreditAdviceDetailColumns: GridColDef[] = [
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  { field: 'ma_dt', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_dt', headerName: 'Tên đối tượng', width: 200 },
  { field: 'du_no', headerName: 'Dư công nợ', type: 'number', width: 120 },
  { field: 'tk_co', headerName: 'Tài khoản có', width: 120 },
  { field: 'ten_tk', headerName: 'Tên tài khoản', width: 200 }
];
