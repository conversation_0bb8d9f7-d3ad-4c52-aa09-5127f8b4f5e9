import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import {
  khachHangSearchColumns,
  accountSearchColumns,
  chiPhiSearchColumns,
  QUERY_KEYS
} from '@/constants';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getBankFeeColumns = (): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200,
    flex: 1,
    renderCell: (params: any) => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || ''}
        onValueChange={(newValue: any) => {
          params.row.dien_giai = newValue;
        }}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => {
          params.row.ma_kh_data = row;
          params.api.updateRows([params.row]);
        }}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: (params: any) => (
      <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} disabled />
    )
  },
  {
    field: 'tien_cp_nt',
    headerName: 'Phí VNĐ',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='tien_cp_nt'
        type='number'
        value={params.row.tien_cp_nt || 0}
        onValueChange={(newValue: any) => {
          params.row.tien_cp_nt = newValue;
        }}
      />
    )
  },
  {
    field: 'so_ct0',
    headerName: 'Số hoá đơn',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0 || ''}
        onValueChange={(newValue: any) => {
          params.row.so_ct0 = newValue;
        }}
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='so_ct2'
        type='text'
        value={params.row.so_ct2 || ''}
        onValueChange={(newValue: any) => {
          params.row.so_ct2 = newValue;
        }}
      />
    )
  },
  {
    field: 'tk_cpnh',
    headerName: 'Tk phí',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_cpnh_data?.code || ''}
        onRowSelection={(row: any) => {
          params.row.tk_cpnh_data = row;
          params.api.updateRows([params.row]);
        }}
      />
    )
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VNĐ',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt || 0}
        onValueChange={(newValue: any) => {
          params.row.t_thue_nt = newValue;
        }}
      />
    )
  }
];
