import React, { forwardRef, useImperativeHandle } from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { BankFeeInputTableActionBar } from './BankFeeInputTableActionBar';
import { useBankFeeRows } from './hooks/useBankFeeRows';
import { getBankFeeColumns } from './columns';
import { FormMode } from '@/types/form';

interface BankFeeTabProps {
  value: any[];
  onChange?: (newValue: any[]) => void;
  formMode: FormMode;
}

export const BankFeeTab = forwardRef<any, BankFeeTabProps>(({ value, onChange, formMode }, ref) => {
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    getCurrentData
  } = useBankFeeRows(value, onChange);

  // Expose getCurrentData to parent component
  useImperativeHandle(ref, () => ({
    getCurrentData
  }));

  return (
    <div className='h-[270px] w-full'>
      <InputTable
        rows={rows}
        onRowClick={handleRowClick}
        selectedRowId={selectedRowUuid || undefined}
        columns={getBankFeeColumns()}
        getRowId={row => row?.uuid || row?.id || ''}
        actionButtons={
          <BankFeeInputTableActionBar
            mode={formMode}
            handleAddRow={handleAddRow}
            handleDeleteRow={handleDeleteRow}
            handleCopyRow={handleCopyRow}
            handlePasteRow={handlePasteRow}
            handleMoveRow={handleMoveRow}
          />
        }
      />
    </div>
  );
});

BankFeeTab.displayName = 'BankFeeTab';
