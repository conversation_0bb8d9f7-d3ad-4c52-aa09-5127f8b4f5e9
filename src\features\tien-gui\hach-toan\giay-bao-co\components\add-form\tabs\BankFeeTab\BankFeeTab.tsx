import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { BankFeeInputTableActionBar } from './BankFeeInputTableActionBar';
import { useBankFeeRows } from './hooks/useBankFeeRows';
import { getBankFeeTableColumns } from './columns';
import { FormMode } from '@/types/form';

interface BankFeeTabProps {
  value: any[];
  onChange?: (newValue: any[]) => void;
  formMode: FormMode;
}

export const BankFeeTab: React.FC<BankFeeTabProps> = ({ value, onChange, formMode }) => {
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useBankFeeRows(value, onChange);

  return (
    <div className='h-[270px] w-full'>
      <InputTable
        rows={rows}
        onRowClick={handleRowClick}
        selectedRowId={selectedRowUuid || undefined}
        columns={getBankFeeTableColumns(handleCellValueChange)}
        getRowId={row => row?.uuid || row?.id || ''}
        actionButtons={
          <BankFeeInputTableActionBar
            mode={formMode}
            handleAddRow={handleAddRow}
            handleDeleteRow={handleDeleteRow}
            handleCopyRow={handleCopyRow}
            handlePasteRow={handlePasteRow}
            handleMoveRow={handleMoveRow}
          />
        }
      />
    </div>
  );
};
