import { hanThanhToanSearchColumns, quyenChungTuSearchColumns } from '@/constants/search-columns';
import { SearchField, FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';
import { FormMode } from '@/types/form';

interface Props {
  value: any[];
  onChange?: (newValue: any[]) => void;
  formMode: FormMode;
}

export const PaymentInfoTab = ({ value, onChange, formMode }: Props) => {
  return (
    <div className='min-w-[800px] py-4'>
      <FormField
        label='Theo dõi thanh toán'
        className='grid grid-cols-[200px,1fr] items-center'
        name='hd_yn'
        type='checkbox'
        disabled={formMode === 'view'}
      />
      <div className='flex items-center'>
        <Label className='w-32 min-w-32'><PERSON><PERSON> chứng từ</Label>
        <SearchField
          type='text'
          disabled={formMode === 'view'}
          columnDisplay='ma_nk'
          searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}/`}
          dialogTitle='Danh mục chứng từ'
          searchColumns={quyenChungTuSearchColumns}
        />
      </div>
      <FormField
        label='Ngày chứng từ'
        className='grid grid-cols-[120px,1fr] items-center'
        type='date'
        name='ngay_ct0'
        disabled={formMode === 'view'}
      />
      <div className='flex items-center'>
        <Label className='w-32 min-w-32'>Mã thanh toán</Label>
        <SearchField
          className='items-center'
          name='ma_tt'
          disabled={formMode === 'view'}
          columnDisplay={'ma_tt'}
          displayRelatedField={'ten_tt'}
          searchColumns={hanThanhToanSearchColumns}
          searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
        />
      </div>
    </div>
  );
};
