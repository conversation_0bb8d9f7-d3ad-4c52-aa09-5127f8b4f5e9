import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import {
  khachHangSearchColumns,
  accountSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  chiPhiSearchColumns,
  QUERY_KEYS
} from '@/constants';
import {
  KhachHang,
  TaiKhoan,
  VuViec,
  HopDong,
  DotThanhToan,
  KheUoc,
  Phi,
  VatTu,
  ChiPhi
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailItemColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: '<PERSON><PERSON><PERSON> gi<PERSON>i',
    width: 300,
    renderCell: params => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.description || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'description', newValue)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 150,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 150,
    renderCell: params => (
      <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} disabled />
    )
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 150,
    renderCell: params => (
      <CellField
        name='du_cn'
        type='number'
        value={params.row.debtBalance || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'debtBalance', newValue)}
      />
    )
  },
  {
    field: 'id_hd',
    headerName: 'Hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='id_hd'
        type='text'
        value={params.row.invoice || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'invoice', newValue)}
      />
    )
  },
  {
    field: 'so_ct0_hd',
    headerName: 'Số hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='so_ct0_hd'
        type='text'
        value={params.row.invoiceNumber || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invoiceNumber', newValue)
        }
      />
    )
  },
  {
    field: 'ngay_ct_hd',
    headerName: 'Ngày hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='ngay_ct_hd'
        type='date'
        value={params.row.invoiceDate || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'invoiceDate', newValue)}
      />
    )
  },
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 150,
    renderCell: params => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_co_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'tk_co_data', row)}
      />
    )
  },
  {
    field: 'ma_nt_hd',
    headerName: 'Ngoại tệ',
    width: 150,
    renderCell: params => (
      <CellField
        name='ma_nt_hd'
        type='text'
        value={params.row.currency || ''}
        disabled={true}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'currency', newValue)}
      />
    )
  },
  {
    field: 'ty_gia_hd',
    headerName: 'Tỷ giá hđ',
    width: 150,
    renderCell: params => (
      <CellField
        name='ty_gia_hd'
        type='number'
        value={params.row.invoiceExchangeRate || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invoiceExchangeRate', newValue)
        }
      />
    )
  },
  {
    field: 'tien_hd_nt',
    headerName: 'Tiền trên hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='tien_hd_nt'
        type='number'
        value={params.row.invoiceAmount || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invoiceAmount', newValue)
        }
      />
    )
  },
  {
    field: 'da_pb_nt',
    headerName: 'Đã phân bổ',
    width: 150,
    renderCell: params => (
      <CellField
        name='da_pb_nt'
        type='number'
        value={params.row.allocatedAmount || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'allocatedAmount', newValue)
        }
      />
    )
  },
  {
    field: 'cl_nt',
    headerName: 'Còn lại',
    width: 150,
    renderCell: params => (
      <CellField
        name='cl_nt'
        type='number'
        value={params.row.remainingAmount || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'remainingAmount', newValue)
        }
      />
    )
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VNĐ',
    width: 150,
    renderCell: params => (
      <CellField
        name='tien_nt'
        type='number'
        value={params.row.amountInVND || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'amountInVND', newValue)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 150,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vv || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 150,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 150,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 150,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint='/'
        searchColumns={[]}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => (
      <SearchField<ChiPhi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_cp0_data', row)}
      />
    )
  }
];
