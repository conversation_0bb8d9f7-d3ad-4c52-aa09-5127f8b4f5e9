import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import {
  khachHangSearchColumns,
  accountSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  boPhanSearchColumns,
  chiPhiSearchColumns,
  QUERY_KEYS
} from '@/constants';
import { KhachHang, TaiKhoan, HopDong, DotThanhToan, KheUoc, Phi, VatTu } from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailItemColumns = (
  ma_ngv: string,
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: '<PERSON>ễn gi<PERSON>',
    width: 300,
    renderCell: (params: any) => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.description || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'description', newValue)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: (params: any) => (
      <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} disabled />
    )
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='du_cn'
        type='number'
        value={params.row.debtBalance || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'debtBalance', newValue)}
      />
    )
  },
  // Conditional invoice-related columns (only show when ma_ngv === 'hoa_don')
  ...(ma_ngv === 'hoa_don'
    ? [
        {
          field: 'id_hd',
          headerName: 'Hóa đơn',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='id_hd'
              type='text'
              value={params.row.invoice || ''}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'invoice', newValue)
              }
            />
          )
        },
        {
          field: 'so_ct0_hd',
          headerName: 'Số hóa đơn',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='so_ct0_hd'
              type='text'
              value={params.row.invoiceNumber || ''}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'invoiceNumber', newValue)
              }
            />
          )
        },
        {
          field: 'ngay_ct_hd',
          headerName: 'Ngày hóa đơn',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='ngay_ct_hd'
              type='date'
              value={params.row.invoiceDate || ''}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'invoiceDate', newValue)
              }
            />
          )
        },
        {
          field: 'tk_co',
          headerName: 'Tài khoản có',
          width: 150,
          renderCell: (params: any) => (
            <SearchField<TaiKhoan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              columnDisplay='code'
              dialogTitle='Danh mục tài khoản'
              value={params.row.tk_co_data?.code || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'tk_co_data', row)}
            />
          )
        },
        {
          field: 'ma_nt_hd',
          headerName: 'Ngoại tệ',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='ma_nt_hd'
              type='text'
              value={params.row.currency || ''}
              disabled={true}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'currency', newValue)
              }
            />
          )
        },
        {
          field: 'ty_gia_hd',
          headerName: 'Tỷ giá hđ',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='ty_gia_hd'
              type='number'
              value={params.row.invoiceExchangeRate || 0}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'invoiceExchangeRate', newValue)
              }
            />
          )
        },
        {
          field: 'tien_hd_nt',
          headerName: 'Tiền trên hóa đơn',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='tien_hd_nt'
              type='number'
              value={params.row.invoiceAmount || 0}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'invoiceAmount', newValue)
              }
            />
          )
        },
        {
          field: 'da_pb_nt',
          headerName: 'Đã phân bổ',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='da_pb_nt'
              type='number'
              value={params.row.allocatedAmount || 0}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'allocatedAmount', newValue)
              }
            />
          )
        },
        {
          field: 'cl_nt',
          headerName: 'Còn lại',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='cl_nt'
              type='number'
              value={params.row.remainingAmount || 0}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'remainingAmount', newValue)
              }
            />
          )
        },
        {
          field: 'tien_nt',
          headerName: 'Tiền VNĐ',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='tien_nt'
              type='number'
              value={params.row.amountInVND || 0}
              onValueChange={(newValue: any) =>
                onCellValueChange(params.row.uuid || params.row.id, 'amountInVND', newValue)
              }
            />
          )
        }
      ]
    : []),
  // Regular columns that always show
  {
    field: 'department',
    headerName: 'Bộ phận',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'project',
    headerName: 'Vụ việc',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vv'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vv || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'contract',
    headerName: 'Hợp đồng',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'paymentPhase',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'agreement',
    headerName: 'Khế ước',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'fee',
    headerName: 'Phí',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'product',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'productionOrder',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint='/'
        searchColumns={[]}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'invalidDocument',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_cp0_data', row)}
      />
    )
  }
];
