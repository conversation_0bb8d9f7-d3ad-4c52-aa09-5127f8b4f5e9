import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailItemColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'description',
    headerName: 'Diễn giải',
    width: 300,
    renderCell: params => (
      <CellField
        name='description'
        type='text'
        value={params.row.description || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'description', newValue)}
      />
    )
  },
  {
    field: 'targetCode',
    headerName: 'Mã đối tượng',
    width: 150,
    renderCell: params => (
      <CellField
        name='targetCode'
        type='text'
        value={params.row.targetCode || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'targetCode', newValue)}
      />
    )
  },
  {
    field: 'targetName',
    headerName: 'Tên đối tượng',
    width: 150,
    renderCell: params => (
      <CellField
        name='targetName'
        type='text'
        value={params.row.targetName || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'targetName', newValue)}
      />
    )
  },
  {
    field: 'debtBalance',
    headerName: 'Dư công nợ',
    width: 150,
    renderCell: params => (
      <CellField
        name='debtBalance'
        type='number'
        value={params.row.debtBalance || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'debtBalance', newValue)}
      />
    )
  },
  {
    field: 'invoice',
    headerName: 'Hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='invoice'
        type='text'
        value={params.row.invoice || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'invoice', newValue)}
      />
    )
  },
  {
    field: 'invoiceNumber',
    headerName: 'Số hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='invoiceNumber'
        type='text'
        value={params.row.invoiceNumber || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invoiceNumber', newValue)
        }
      />
    )
  },
  {
    field: 'invoiceDate',
    headerName: 'Ngày hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='invoiceDate'
        type='date'
        value={params.row.invoiceDate || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'invoiceDate', newValue)}
      />
    )
  },
  {
    field: 'creditAccount',
    headerName: 'Tài khoản có',
    width: 150,
    renderCell: params => (
      <CellField
        name='creditAccount'
        type='text'
        value={params.row.creditAccount || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'creditAccount', newValue)
        }
      />
    )
  },
  {
    field: 'currency',
    headerName: 'Ngoại tệ',
    width: 150,
    renderCell: params => (
      <CellField
        name='currency'
        type='text'
        value={params.row.currency || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'currency', newValue)}
      />
    )
  },
  {
    field: 'invoiceExchangeRate',
    headerName: 'Tỷ giá hđ',
    width: 150,
    renderCell: params => (
      <CellField
        name='invoiceExchangeRate'
        type='number'
        value={params.row.invoiceExchangeRate || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invoiceExchangeRate', newValue)
        }
      />
    )
  },
  {
    field: 'invoiceAmount',
    headerName: 'Tiền trên hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='invoiceAmount'
        type='number'
        value={params.row.invoiceAmount || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invoiceAmount', newValue)
        }
      />
    )
  },
  {
    field: 'allocatedAmount',
    headerName: 'Đã phân bổ',
    width: 150,
    renderCell: params => (
      <CellField
        name='allocatedAmount'
        type='number'
        value={params.row.allocatedAmount || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'allocatedAmount', newValue)
        }
      />
    )
  },
  {
    field: 'remainingAmount',
    headerName: 'Còn lại',
    width: 150,
    renderCell: params => (
      <CellField
        name='remainingAmount'
        type='number'
        value={params.row.remainingAmount || 0}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'remainingAmount', newValue)
        }
      />
    )
  },
  {
    field: 'ma_loai_hd',
    headerName: 'Loại hóa đơn',
    width: 150,
    renderCell: params => {
      const value = params.row.ma_loai_hd;
      const displayValue = (() => {
        switch (value) {
          case '0':
            return '0. Không có hoá đơn';
          case '1':
            return '1. Hoá đơn GTGT đã tách thuế';
          case '2':
            return '2. Hoá đơn GTGT không tách thuế';
          case '3':
            return '3. Hoá đơn bán hàng thông thường';
          default:
            return value || '';
        }
      })();

      return (
        <CellField
          name='ma_loai_hd'
          type='select'
          value={value || ''}
          options={[
            { value: '0', label: '0. Không có hoá đơn' },
            { value: '1', label: '1. Hoá đơn GTGT đã tách thuế' },
            { value: '2', label: '2. Hoá đơn GTGT không tách thuế' },
            { value: '3', label: '3. Hoá đơn bán hàng thông thường' }
          ]}
          onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_loai_hd', newValue)}
        />
      );
    }
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mã mẫu báo cáo',
    width: 150,
    renderCell: params => {
      const value = params.row.ma_mau_bc;
      const displayValue = (() => {
        switch (value) {
          case '3':
            return '3. Hóa đơn giá trị gia tăng';
          case '4':
            return '4. Hàng hóa, dịch vụ mua vào không có hóa đơn';
          case '5':
            return '5. Hóa đơn bán hàng thông thường';
          default:
            return value || '';
        }
      })();

      return (
        <CellField
          name='ma_mau_bc'
          type='select'
          value={value || ''}
          options={[
            { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
            { value: '4', label: '4. Hàng hóa, dịch vụ mua vào không có hóa đơn' },
            { value: '5', label: '5. Hóa đơn bán hàng thông thường' }
          ]}
          onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_mau_bc', newValue)}
        />
      );
    }
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất thuế',
    width: 150,
    renderCell: params => {
      const value = params.row.ma_tc_thue;
      const displayValue = (() => {
        switch (value) {
          case '1':
            return '1. Hàng hoá, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế';
          case '2':
            return '2. Hàng hoá, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT';
          case '3':
            return '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế';
          default:
            return value || '';
        }
      })();

      return (
        <CellField
          name='ma_tc_thue'
          type='select'
          value={value || ''}
          options={[
            {
              value: '1',
              label:
                '1. Hàng hoá, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế'
            },
            { value: '2', label: '2. Hàng hoá, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT' },
            { value: '3', label: '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế' }
          ]}
          onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'ma_tc_thue', newValue)}
        />
      );
    }
  },
  {
    field: 'amountInVND',
    headerName: 'Tiền VNĐ',
    width: 150,
    renderCell: params => (
      <CellField
        name='amountInVND'
        type='number'
        value={params.row.amountInVND || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'amountInVND', newValue)}
      />
    )
  },
  {
    field: 'department',
    headerName: 'Bộ phận',
    width: 150,
    renderCell: params => (
      <CellField
        name='department'
        type='text'
        value={params.row.department || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'department', newValue)}
      />
    )
  },
  {
    field: 'project',
    headerName: 'Vụ việc',
    width: 150,
    renderCell: params => (
      <CellField
        name='project'
        type='text'
        value={params.row.project || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'project', newValue)}
      />
    )
  },
  {
    field: 'contract',
    headerName: 'Hợp đồng',
    width: 150,
    renderCell: params => (
      <CellField
        name='contract'
        type='text'
        value={params.row.contract || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'contract', newValue)}
      />
    )
  },
  {
    field: 'paymentPhase',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => (
      <CellField
        name='paymentPhase'
        type='text'
        value={params.row.paymentPhase || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'paymentPhase', newValue)}
      />
    )
  },
  {
    field: 'agreement',
    headerName: 'Khế ước',
    width: 150,
    renderCell: params => (
      <CellField
        name='agreement'
        type='text'
        value={params.row.agreement || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'agreement', newValue)}
      />
    )
  },
  {
    field: 'fee',
    headerName: 'Phí',
    width: 150,
    renderCell: params => (
      <CellField
        name='fee'
        type='text'
        value={params.row.fee || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'fee', newValue)}
      />
    )
  },
  {
    field: 'product',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: params => (
      <CellField
        name='product'
        type='text'
        value={params.row.product || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'product', newValue)}
      />
    )
  },
  {
    field: 'productionOrder',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: params => (
      <CellField
        name='productionOrder'
        type='text'
        value={params.row.productionOrder || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'productionOrder', newValue)
        }
      />
    )
  },
  {
    field: 'invalidDocument',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => (
      <CellField
        name='invalidDocument'
        type='text'
        value={params.row.invalidDocument || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invalidDocument', newValue)
        }
      />
    )
  }
];
