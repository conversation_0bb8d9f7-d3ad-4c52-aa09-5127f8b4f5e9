import { ChangeEvent, useState } from 'react';
import { UploadCloud } from 'lucide-react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface Props {
  value: any[];
  onChange?: (newValue: any[]) => void;
  onFileChange?: (file: File | null) => void;
  formMode: FormMode;
}

export const OtherTab = ({ value, onChange, onFileChange, formMode }: Props) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);
  };

  return (
    <div className='min-w-[800px] py-4'>
      <FormField
        className='w-[250px] items-center'
        label='Mã đối tượng'
        type='text'
        name='ma_kh'
        disabled={true}
      />
      <FormField
        label='Kèm theo'
        className='w-[250px] items-center'
        type='number'
        name='so_ct_goc'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Chứng từ gốc'
        className='w-[250px] items-center'
        name='so_ct_goc'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Tên ngân hàng'
        className='w-[250px] items-center'
        name='ten_nh'
        type='text'
        disabled={true}
      />
      <div className='flex items-center gap-4'>
        <Label htmlFor='xfile' className='min-w-[100px] text-sm font-medium'>
          Chọn file
        </Label>
        <div className='flex items-center gap-2'>
          <Label
            htmlFor='xfile'
            className='flex cursor-pointer items-center gap-2 rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90'
          >
            <UploadCloud className='h-4 w-4' />
            <span>Chọn file</span>
          </Label>
          <Input
            id='xfile'
            type='file'
            className='hidden'
            onChange={handleFileChange}
            disabled={formMode === 'view'}
          />
          {selectedFile && <span className='text-sm text-gray-600'>{selectedFile.name}</span>}
        </div>
      </div>
    </div>
  );
};
