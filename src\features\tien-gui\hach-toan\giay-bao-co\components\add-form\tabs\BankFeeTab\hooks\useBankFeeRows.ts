import { useState, useEffect, useCallback } from 'react';
import { BankFeeItem } from '@/features/tien-gui/hach-toan/giay-bao-co/schemas';

export type BankFeeRow = BankFeeItem;

export function useBankFeeRows(initialRows: any[] = [], onChange?: (newValue: any[]) => void) {
  const [rows, setRows] = useState<BankFeeRow[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<BankFeeRow | null>(null);

  // Sync with external value changes
  useEffect(() => {
    setRows(initialRows);
  }, [initialRows]);

  // Function to get current data (called when submit)
  const getCurrentData = useCallback(() => {
    return rows;
  }, [rows]);

  const generateUuid = () => `bank-fee-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const handleAddRow = useCallback(() => {
    const newRow: BankFeeRow = {
      uuid: generateUuid(),
      customerCode: '',
      customerName: '',
      feeCode: '',
      bankFeeName: '',
      feeVND: 0,
      invoiceNumber: '',
      symbol: '',
      invoiceDate: new Date().toISOString().split('T')[0],
      description: '',
      feeAccount: '',
      counterAccount: '',
      taxAccount: '',
      taxRate: 0,
      taxAmountVND: 0,
      department: '',
      project: '',
      contract: '',
      paymentPhase: '',
      agreement: '',
      feeNote: '',
      product: '',
      productionOrder: '',
      invalidDocument: '',
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    const updatedRows = [...rows, newRow];
    setRows(updatedRows);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid || '');
    setSelectedRow(newRow);
  }, [rows]);

  const handleDeleteRow = useCallback(() => {
    let updatedRows: BankFeeRow[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => (row.uuid || row.id) !== selectedRowUuid);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
    }

    setRows(updatedRows);

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || lastRow.id || null);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  }, [rows, selectedRowUuid]);

  const handleCopyRow = useCallback(() => {
    if (!selectedRow) return;

    const { uuid, id, ...rowDataWithoutUuid } = selectedRow;

    const newRow: BankFeeRow = {
      ...rowDataWithoutUuid,
      uuid: generateUuid(),
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    const updatedRows = [...rows, newRow];
    setRows(updatedRows);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid || '');
    setSelectedRow(newRow);
  }, [rows, selectedRow]);

  const handlePasteRow = useCallback(() => {
    if (!selectedRow) return;

    const { uuid, id, ...rowDataWithoutUuid } = selectedRow;

    const newRow: BankFeeRow = {
      ...rowDataWithoutUuid,
      uuid: generateUuid(),
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    let newRows: BankFeeRow[];
    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => (row.uuid || row.id) === selectedRowUuid);
      if (selectedIndex === -1) return;

      newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);
    } else {
      newRows = [...rows, newRow];
    }

    setRows(newRows);

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid || '');
    setSelectedRow(newRow);
  }, [rows, selectedRow, selectedRowUuid]);

  const handleMoveRow = useCallback(
    (direction: 'up' | 'down') => {
      if (!selectedRowUuid) return;

      const currentIndex = rows.findIndex(row => (row.uuid || row.id) === selectedRowUuid);
      if (currentIndex === -1) return;

      const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

      if (newIndex === currentIndex) return;

      const newRows = [...rows];
      const [movedRow] = newRows.splice(currentIndex, 1);
      newRows.splice(newIndex, 0, movedRow);

      setRows(newRows);

      // Ensure selection follows the moved row
      setSelectedRow(movedRow);
    },
    [rows, selectedRowUuid]
  );

  const handleRowClick = (params: any) => {
    const rowUuid = params.id?.toString() || params.row?.uuid || params.row?.id;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    handleRowClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    getCurrentData
  };
}
