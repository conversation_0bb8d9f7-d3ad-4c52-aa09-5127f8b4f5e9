import { useState, useEffect, useCallback, useRef } from 'react';
import { BankFeeItem } from '@/features/tien-gui/hach-toan/giay-bao-co/schemas';

export type BankFeeRow = BankFeeItem;

export function useBankFeeRows(initialRows: any[] = [], onChange?: (newValue: any[]) => void) {
  const [rows, setRows] = useState<BankFeeRow[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<BankFeeRow | null>(null);

  // Refs for debouncing
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const onChangeRef = useRef(onChange);

  // Update onChange ref when it changes
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // Sync with external value changes
  useEffect(() => {
    setRows(initialRows);
  }, [initialRows]);

  // Debounced onChange notification to prevent infinite loops
  const notifyParentChange = useCallback((newRows: BankFeeRow[]) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      onChangeRef.current?.(newRows);
    }, 100); // 100ms debounce
  }, []);

  // Notify parent of changes with debounce
  useEffect(() => {
    notifyParentChange(rows);

    // Cleanup on unmount
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [rows, notifyParentChange]);

  const generateUuid = () => `bank-fee-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const handleAddRow = () => {
    const newRow: BankFeeRow = {
      uuid: generateUuid(),
      customerCode: '',
      customerName: '',
      feeCode: '',
      bankFeeName: '',
      feeVND: 0,
      invoiceNumber: '',
      symbol: '',
      invoiceDate: new Date().toISOString().split('T')[0],
      description: '',
      feeAccount: '',
      counterAccount: '',
      taxAccount: '',
      taxRate: 0,
      taxAmountVND: 0,
      department: '',
      project: '',
      contract: '',
      paymentPhase: '',
      agreement: '',
      feeNote: '',
      product: '',
      productionOrder: '',
      invalidDocument: '',
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    const updatedRows = [...rows, newRow];
    setRows(updatedRows);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid || '');
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    let updatedRows: BankFeeRow[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => (row.uuid || row.id) !== selectedRowUuid);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
    }

    setRows(updatedRows);

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || lastRow.id || null);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, id, ...rowDataWithoutUuid } = selectedRow;

    const newRow: BankFeeRow = {
      ...rowDataWithoutUuid,
      uuid: generateUuid(),
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    const updatedRows = [...rows, newRow];
    setRows(updatedRows);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid || '');
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, id, ...rowDataWithoutUuid } = selectedRow;

    const newRow: BankFeeRow = {
      ...rowDataWithoutUuid,
      uuid: generateUuid(),
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => (row.uuid || row.id) === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);
      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid || '');
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => (row.uuid || row.id) === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    // Ensure selection follows the moved row
    setSelectedRow(movedRow);
  };

  const handleCellValueChange = useCallback((rowUuid: string, field: string, newValue: any) => {
    setRows(currentRows => {
      const rowIndex = currentRows.findIndex(row => (row.uuid || row.id) === rowUuid);
      if (rowIndex === -1) return currentRows;

      const currentRow = currentRows[rowIndex];

      // Check if value actually changed to prevent unnecessary updates
      if (currentRow[field] === newValue) {
        return currentRows;
      }

      const updatedRows = [...currentRows];
      updatedRows[rowIndex] = {
        ...currentRow,
        [field]: newValue,
        updated: new Date().toISOString()
      };

      return updatedRows;
    });

    // Update selected row if it's the one being changed
    setSelectedRow(currentSelected => {
      if (currentSelected && (currentSelected.uuid || currentSelected.id) === rowUuid) {
        return { ...currentSelected, [field]: newValue };
      }
      return currentSelected;
    });
  }, []);

  const handleRowClick = (params: any) => {
    const rowUuid = params.id?.toString() || params.row?.uuid || params.row?.id;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    handleRowClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  };
}
