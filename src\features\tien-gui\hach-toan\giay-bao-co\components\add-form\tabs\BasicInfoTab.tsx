import { accountSearchColumns, bankAccountSearchColumns, QUERY_KEYS, quyenChungTuSearchColumns } from '@/constants';
import { QuyenChungTu, TaiKhoan, TaiKhoanNganHang } from '@/types/schemas';
import { SearchField, FormField, UnitDropdown } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface Props {
  formMode: FormMode;
  selectedTaiKhoanNo: TaiKhoan | null;
  setSelectedTaiKhoanNo: (value: TaiKhoan | null) => void;
  selectedTaiKhoanNganHang: TaiKhoanNganHang | null;
  setSelectedTaiKhoanNganHang: (value: TaiKhoanNganHang | null) => void;
}

export const BasicInfoTab = ({
  formMode,
  selectedTaiKhoanNo,
  setSelectedTaiKhoanNo,
  selectedTaiKhoanNganHang,
  setSelectedTaiKhoanNganHang
}: Props) => (
  <div className='space-y-2 p-4'>
    <div className='flex flex-col gap-6 lg:flex-row lg:gap-8'>
      <div className='flex-1 space-y-2'>
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Loại chứng từ</Label>
          <div className='max-w-md flex-1'>
            <FormField
              type='select'
              options={[
                { value: 'hoa_don', label: '1. Theo hóa đơn' },
                { value: 'doi_tuong', label: '2. Theo đối tượng' },
                { value: 'thu_khac', label: '3. Thu khác' }
              ]}
              name='ma_ngv'
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        {/* Address field */}
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Địa chỉ</Label>
          <div className='flex-1'>
            <FormField type='text' name='dia_chi' disabled={formMode === 'view'} />
          </div>
        </div>

        {/* Payer field */}
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Người nộp tiền</Label>
          <div className='flex-1'>
            <FormField type='text' name='ong_ba' disabled={formMode === 'view'} />
          </div>
        </div>

        {/* Description field */}
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Diễn giải</Label>
          <div className='flex-1'>
            <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex flex-col gap-3 lg:flex-row lg:gap-4'>
          <div className='flex items-center gap-2 lg:flex-1'>
            <Label className='w-40 min-w-40 text-left lg:w-32 lg:min-w-32'>Ngân hàng</Label>
            <div className='flex-1'>
              <SearchField<TaiKhoanNganHang>
                type='text'
                displayRelatedField='name'
                columnDisplay='tknh'
                className='w-[180px]'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}`}
                searchColumns={bankAccountSearchColumns}
                dialogTitle='Danh mục tài khoản'
                value={selectedTaiKhoanNganHang?.ma_tai_khoan || ''}
                onRowSelection={setSelectedTaiKhoanNganHang}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
          <div className='flex items-center gap-2 lg:flex-1'>
            <Label className='w-40 min-w-40 text-left lg:w-32 lg:min-w-32'>Tài khoản nợ</Label>
            <div className='flex-1'>
              <SearchField<TaiKhoan>
                type='text'
                displayRelatedField='name'
                columnDisplay='code'
                className='w-[180px]'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
                searchColumns={accountSearchColumns}
                dialogTitle='Danh mục tài khoản'
                value={selectedTaiKhoanNo?.code || ''}
                onRowSelection={setSelectedTaiKhoanNo}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Right column - Secondary form fields */}
      <div className='w-full space-y-3 lg:w-80'>
        {/* Document number field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Số chứng từ</Label>
          <SearchField<QuyenChungTu>
            type='text'
            disabled={formMode === 'view'}
            columnDisplay='ma_nk'
            searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}/`}
            dialogTitle='Danh mục chứng từ'
            searchColumns={quyenChungTuSearchColumns}
          />
        </div>
        {/* Document date field */}
        <div className='flex items-center gap-2'>
          <Label className='w-32 min-w-32 text-left'>Ngày chứng từ</Label>
          <div className='flex-1'>
            <FormField type='date' name='ngay_ct' disabled={formMode === 'view'} />
          </div>
        </div>

        {/* Creation date field */}
        <div className='flex items-center gap-2'>
          <Label className='w-32 min-w-32 text-left'>Ngày lập chứng từ</Label>
          <div className='flex-1'>
            <FormField name='date' type='date' disabled={formMode === 'view'} />
          </div>
        </div>

        {/* Foreign currency and exchange rate - responsive row */}
        <div className='flex flex-col gap-2 sm:flex-row sm:items-center'>
          <div className='flex items-center gap-2 sm:flex-1'>
            <Label className='w-32 min-w-32 text-left sm:w-20 sm:min-w-20'>Ngoại tệ</Label>
            <div className='flex-1'>
              <FormField
                type='select'
                name='foreignCurrency'
                options={[
                  { value: 'usd', label: 'USD' },
                  { value: 'eur', label: 'EUR' },
                  { value: 'vnd', label: 'VND' },
                  { value: 'jpy', label: 'JPY' }
                ]}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
          <div className='flex items-center gap-2 sm:flex-1'>
            <Label className='w-32 min-w-32 text-left sm:w-16 sm:min-w-16'>Tỷ giá</Label>
            <div className='flex-1'>
              <FormField type='number' name='exchangeRate' disabled={formMode === 'view'} />
            </div>
          </div>
        </div>
        <UnitDropdown formMode={formMode} />

        {/* Status field */}
        <div className='flex items-center gap-2'>
          <Label className='w-32 min-w-32 text-left'>Trạng thái</Label>
          <div className='flex-1'>
            <FormField
              name='status'
              type='select'
              disabled={formMode === 'view'}
              options={[
                { value: 'Đã ghi sổ', label: 'Đã ghi sổ' },
                { value: 'Chờ duyệt', label: 'Chờ duyệt' },
                { value: 'Chưa duyệt', label: 'Chưa duyệt' }
              ]}
            />
          </div>
        </div>

        {/* Data received checkbox */}
        <div className='flex items-center gap-2'>
          <FormField label='Dữ liệu được nhận' name='dataReceived' type='checkbox' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  </div>
);
