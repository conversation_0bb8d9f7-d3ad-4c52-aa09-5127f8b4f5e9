import { useWatch } from 'react-hook-form';
import React from 'react';
import { DetailItemInputTableActionBar } from './DetailItemInputTableActionBar';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { useDetailItemRows } from './hooks/useDetailItemRows';
import { getDetailItemColumns } from './cols';
import { FormMode } from '@/types/form';

interface DetailItemTabProps {
  value: any[];
  onChange?: (newValue: any[]) => void;
  formMode: FormMode;
}

export const DetailItemTab: React.FC<DetailItemTabProps> = ({ value, onChange, formMode }) => {
  const type = useWatch({ name: 'ma_ngv', defaultValue: '1' });

  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useDetailItemRows(value, onChange);

  return (
    <div className='h-[500px] w-full'>
      <InputTable
        rows={rows}
        onRowClick={handleRowClick}
        selectedRowId={selectedRowUuid || undefined}
        columns={getDetailItemColumns(type, handleCellValueChange)}
        getRowId={row => row?.uuid || row?.id || ''}
        actionButtons={
          <DetailItemInputTableActionBar
            mode={formMode}
            handleAddRow={handleAddRow}
            handleDeleteRow={handleDeleteRow}
            handleCopyRow={handleCopyRow}
            handlePasteRow={handlePasteRow}
            handleMoveRow={handleMoveRow}
          />
        }
      />
    </div>
  );
};
