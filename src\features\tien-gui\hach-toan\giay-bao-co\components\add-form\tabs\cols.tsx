import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import { CellField } from '@/components/custom/arito/custom-input-table/components';

export const getBankFeeColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: params => (
      <CellField
        name='customerCode'
        type='text'
        value={params.row.customerCode || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'customerCode', newValue)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => (
      <CellField
        name='customerName'
        type='text'
        value={params.row.customerName || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'customerName', newValue)}
      />
    )
  },
  {
    field: 'ma_cpnh',
    headerName: 'Mã chi phí',
    width: 150,
    renderCell: params => (
      <CellField
        name='feeCode'
        type='text'
        value={params.row.feeCode || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'feeCode', newValue)}
      />
    )
  },
  {
    field: 'ten_cpnh',
    headerName: 'Tên chi phí ngân hàng',
    width: 200,
    renderCell: params => (
      <CellField
        name='bankFeeName'
        type='text'
        value={params.row.bankFeeName || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'bankFeeName', newValue)}
      />
    )
  },
  {
    field: 'tien_cp_nt',
    headerName: 'Phí VNĐ',
    width: 150,
    renderCell: params => (
      <CellField
        name='feeVND'
        type='number'
        value={params.row.feeVND || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'feeVND', newValue)}
      />
    )
  },
  {
    field: 'so_ct0',
    headerName: 'Số hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='invoiceNumber'
        type='text'
        value={params.row.invoiceNumber || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invoiceNumber', newValue)
        }
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 150,
    renderCell: params => (
      <CellField
        name='symbol'
        type='text'
        value={params.row.symbol || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'symbol', newValue)}
      />
    )
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='invoiceDate'
        type='date'
        value={params.row.invoiceDate || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'invoiceDate', newValue)}
      />
    )
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200,
    flex: 1,
    renderCell: params => (
      <CellField
        name='description'
        type='text'
        value={params.row.description || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'description', newValue)}
      />
    )
  },
  {
    field: 'tk_cpnh',
    headerName: 'Tk phí',
    width: 150,
    renderCell: params => (
      <CellField
        name='feeAccount'
        type='text'
        value={params.row.feeAccount || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'feeAccount', newValue)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'Tk đối ứng',
    width: 150,
    renderCell: params => (
      <CellField
        name='counterAccount'
        type='text'
        value={params.row.counterAccount || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'counterAccount', newValue)
        }
      />
    )
  },
  {
    field: 'tk_thue',
    headerName: 'Tk thuế',
    width: 150,
    renderCell: params => (
      <CellField
        name='taxAccount'
        type='text'
        value={params.row.taxAccount || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'taxAccount', newValue)}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 150,
    renderCell: params => (
      <CellField
        name='taxRate'
        type='number'
        value={params.row.taxRate || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'taxRate', newValue)}
      />
    )
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VNĐ',
    width: 150,
    renderCell: params => (
      <CellField
        name='taxAmountVND'
        type='number'
        value={params.row.taxAmountVND || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'taxAmountVND', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 150,
    renderCell: params => (
      <CellField
        name='department'
        type='text'
        value={params.row.department || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'department', newValue)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 150,
    renderCell: params => (
      <CellField
        name='project'
        type='text'
        value={params.row.project || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'project', newValue)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 150,
    renderCell: params => (
      <CellField
        name='contract'
        type='text'
        value={params.row.contract || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'contract', newValue)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => (
      <CellField
        name='paymentPhase'
        type='text'
        value={params.row.paymentPhase || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'paymentPhase', newValue)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 150,
    renderCell: params => (
      <CellField
        name='agreement'
        type='text'
        value={params.row.agreement || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'agreement', newValue)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 150,
    renderCell: params => (
      <CellField
        name='feeNote'
        type='text'
        value={params.row.feeNote || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'feeNote', newValue)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: params => (
      <CellField
        name='product'
        type='text'
        value={params.row.product || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid || params.row.id, 'product', newValue)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: params => (
      <CellField
        name='productionOrder'
        type='text'
        value={params.row.productionOrder || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'productionOrder', newValue)
        }
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => (
      <CellField
        name='invalidDocument'
        type='text'
        value={params.row.invalidDocument || ''}
        onValueChange={(newValue: any) =>
          onCellValueChange(params.row.uuid || params.row.id, 'invalidDocument', newValue)
        }
      />
    )
  }
];
