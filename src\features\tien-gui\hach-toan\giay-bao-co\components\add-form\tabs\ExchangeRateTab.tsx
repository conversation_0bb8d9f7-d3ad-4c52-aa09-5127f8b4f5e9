import { FormField } from '@/components/custom/arito/form/form-field';
import { FormMode } from '@/types/form';

interface Props {
  value: any[];
  onChange?: (newValue: any[]) => void;
  formMode: FormMode;
}

export const ExchangeRateTab = ({ value, onChange, formMode }: Props) => {
  return (
    <div className='min-w-[800px] py-4'>
      <FormField
        label='Sửa tỷ giá ghi sổ'
        className='grid grid-cols-[200px,1fr] items-center'
        name='tg_dd'
        type='checkbox'
        disabled={formMode === 'view'}
      />
    </div>
  );
};
